<template>
  <div class="category-list">
    <CareCategory
      v-for="category in CARE_CATEGORIES"
      :key="category.categoryKey"
      :categoryType="category.categoryType"
      :categoryValue="categoryValues[category.categoryKey]"
      :categoryUnit="category.categoryUnit"
      :categoryKey="category.categoryKey"
    />
  </div>
</template>

<script>
import CareCategory from "./care-category.ui.vue";

import { CARE_CATEGORIES } from "./constants/care-category.constants";

export default {
  name: "CareCategoryList",

  components: {
    CareCategory,
  },

  props: {
    selectedDate: {
      type: Date,
      required: true,
    },
    detailData: {
      type: Object,
      required: true,
      default: () => ({
        weight: "0",
        water: "0",
        pee: "0",
        bloodPressure: "0/0",
        step: "0",
        woman: "-",
      }),
    },
  },

  data() {
    return {
      CARE_CATEGORIES,
      categoryValues: {
        weight: "0",
        water: "0",
        pee: "0",
        bloodPressure: "0/0",
        step: "0",
        woman: "-",
      },
    };
  },

  methods: {
    checkDetailData(detail, key) {
      if (!detail) return;

      switch (key) {
        case "bloodPressure": {
          for (let i = 0, len = detail.length; i < len; i++) {
            const detailData = detail[i];

            if (
              detailData &&
              typeof detailData === "object" &&
              "systolic" in detailData &&
              "diastolic" in detailData
            ) {
              return `${detailData.systolic}/${detailData.diastolic}`;
            }
            return "0/0";
          }
          return "0/0";
        }
        case "step": {
          if (!detail) return "0";

          const steps = detail.totalStepCount;

          return steps.toLocaleString("ko-KR") ?? 0;
        }
        case "woman":
          return "";
        case "urine": {
          return String(detail.length) ?? "0";
        }
        default: {
          if (!detail) return "-";

          if (detail.length === 0) return "0";

          let sum = 0;

          // 데이터 규격이 똑같다고 가정. { id: number, value: number, createdAt: string }
          for (let i = 0, len = detail.length; i < len; i++) {
            const detailData = detail[i];

            if (
              detailData &&
              typeof detailData === "object" &&
              "value" in detailData
            ) {
              sum += Number(detailData.value) || 0;
            }
          }

          return sum.toLocaleString("ko-KR");
        }
      }
    },
    init(newData) {
      // 데이터가 없을 때
      if (!newData.data || Object.keys(newData.data).length === 0) {
        return;
      }

      Object.entries(newData.data).forEach(([key, value]) => {
        const detailData = this.checkDetailData(value, key);

        if (detailData) {
          if (key === "urine") {
            this.categoryValues["pee"] = detailData;
          } else {
            this.categoryValues[key] = detailData;
          }
        }
      });
    },
  },

  watch: {
    detailData: {
      handler(newData) {
        console.log("watch triggered:", newData);
        this.init(newData);
      },
      deep: true,
      immediate: true, // 컴포넌트 생성 시 즉시 실행
    },
  },

  mounted() {
    // detailData가 실제 데이터를 가지고 있는지 확인
    if (this.detailData.data && Object.keys(this.detailData.data).length > 0) {
      // 기본값이 아닌 실제 데이터가 있는지 확인
      const hasRealData = Object.values(this.detailData.data).some(
        (value) =>
          value !== "0" &&
          value !== null &&
          value !== undefined &&
          (Array.isArray(value) ? value.length > 0 : true)
      );

      if (hasRealData) {
        this.init(this.detailData);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.category-list {
  width: 100%;
  max-width: 450px;
  height: auto;
  padding: 30px;
  background: #ffffff;
}
</style>
