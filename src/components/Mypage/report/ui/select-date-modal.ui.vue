<template>
  <div
    v-show="isModalOpen"
    id="select-date-modal"
    class="select-date-modal"
    @click="closeModal"
    @touchmove="preventBackgroundScroll"
    @wheel="preventBackgroundScroll"
    ref="modal"
  >
    <transition name="slide-up">
      <div v-if="isModalOpen" class="modal-item" @click.stop @touchmove.stop>
        <p>월 선택</p>
        <div class="picker-container">
          <div class="picker-column">
            <div
              class="picker-wrapper"
              ref="yearPicker"
              @scroll="handleYearScroll"
              @touchend="handleYearTouchEnd"
              @mouseup="handleYearTouchEnd"
            >
              <div
                v-for="(year, index) in availableYears"
                :key="year"
                :class="['picker-item', { active: selectedYear === year }]"
                :style="getItemStyle(index, 'year')"
                @click="selectYear(year)"
              >
                {{ year }}
              </div>
            </div>
          </div>
          <div class="picker-column">
            <div
              class="picker-wrapper"
              ref="monthPicker"
              @scroll="handleMonthScroll"
              @touchend="handleMonthTouchEnd"
              @mouseup="handleMonthTouchEnd"
            >
              <div
                v-for="(month, index) in availableMonths"
                :key="month"
                :class="['picker-item', { active: selectedMonth === month }]"
                :style="getItemStyle(index, 'month')"
                @click="selectMonth(month)"
              >
                {{ String(month).padStart(2, "0") }}
              </div>
            </div>
          </div>
        </div>
        <button class="confirm-button" @click="confirmSelection">확인</button>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: "SelectDateModal",
  data() {
    return {
      isModalOpen: false,
      selectedYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1,
      scrollY: 0,
      yearFadeStyles: {},
      monthFadeStyles: {},
      yearScrollTimeout: null,
      monthScrollTimeout: null,
      isYearSnapping: false,
      isMonthSnapping: false,
      // 성능 최적화를 위한 추가 변수들
      yearAnimationFrame: null,
      monthAnimationFrame: null,
      lastYearScrollTop: 0,
      lastMonthScrollTop: 0,
    };
  },
  computed: {
    modalState() {
      return this.$store.state.setting.showSelectDateModal || false;
    },
    availableYears() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let year = 2020; year <= currentYear; year++) {
        years.push(year);
      }
      return years;
    },
    availableMonths() {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      if (this.selectedYear === currentYear) {
        const months = [];
        for (let month = 1; month < currentMonth; month++) {
          months.push(month);
        }
        return months;
      } else {
        return Array.from({ length: 12 }, (_, i) => i + 1);
      }
    },
  },
  watch: {
    modalState(newVal) {
      this.isModalOpen = newVal;
      if (newVal) {
        this.$nextTick(() => {
          this.scrollToSelected();
          setTimeout(() => {
            this.updateYearFade();
            this.updateMonthFade();
          }, 100);
        });
      }
    },
    isModalOpen(newVal) {
      if (newVal) {
        this.scrollY = window.scrollY;
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.top = `-${this.scrollY}px`;
      } else {
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
        document.body.style.top = "";
        window.scrollTo(0, this.scrollY);
      }
    },
    selectedYear() {
      if (
        this.availableMonths.length > 0 &&
        !this.availableMonths.includes(this.selectedMonth)
      ) {
        this.selectedMonth =
          this.availableMonths[this.availableMonths.length - 1];
      }
    },
  },
  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_SELECT_DATE_MODAL", false);
    },
    preventBackgroundScroll(e) {
      if (e.target === this.$refs.modal) {
        e.preventDefault();
      }
    },
    selectYear(year) {
      this.selectedYear = year;
      this.scrollToSelected();
    },
    selectMonth(month) {
      this.selectedMonth = month;
      this.scrollToSelected();
    },
    getItemStyle(index, type) {
      const styles =
        type === "year" ? this.yearFadeStyles : this.monthFadeStyles;
      return styles[index] || {};
    },

    constrainScroll(type) {
      const picker =
        type === "year" ? this.$refs.yearPicker : this.$refs.monthPicker;
      const items =
        type === "year" ? this.availableYears : this.availableMonths;

      if (!picker || items.length === 0) return;

      const itemHeight = 50;
      const containerHeight = 150; // 고정값 사용
      const centerOffset = (containerHeight - itemHeight) / 2;

      // 최소/최대 스크롤 위치 계산
      const minScrollTop = -centerOffset;
      const maxScrollTop = (items.length - 1) * itemHeight - centerOffset;

      const currentScrollTop = picker.scrollTop;

      // 스크롤 위치가 범위를 벗어나면 제한 (부드럽게)
      if (currentScrollTop < minScrollTop) {
        picker.scrollTop = minScrollTop;
      } else if (currentScrollTop > maxScrollTop) {
        picker.scrollTop = maxScrollTop;
      }
    },

    handleYearScroll() {
      if (this.isYearSnapping) return;

      // requestAnimationFrame으로 성능 최적화
      if (this.yearAnimationFrame) {
        cancelAnimationFrame(this.yearAnimationFrame);
      }

      this.yearAnimationFrame = requestAnimationFrame(() => {
        const picker = this.$refs.yearPicker;
        if (!picker) return;

        // 스크롤 위치가 변경되었을 때만 업데이트
        if (Math.abs(picker.scrollTop - this.lastYearScrollTop) > 1) {
          this.lastYearScrollTop = picker.scrollTop;
          this.constrainScroll("year");
          this.updateYearFade();
          this.updateSelectedFromScroll("year");
        }
      });
    },

    handleMonthScroll() {
      if (this.isMonthSnapping) return;

      // requestAnimationFrame으로 성능 최적화
      if (this.monthAnimationFrame) {
        cancelAnimationFrame(this.monthAnimationFrame);
      }

      this.monthAnimationFrame = requestAnimationFrame(() => {
        const picker = this.$refs.monthPicker;
        if (!picker) return;

        // 스크롤 위치가 변경되었을 때만 업데이트
        if (Math.abs(picker.scrollTop - this.lastMonthScrollTop) > 1) {
          this.lastMonthScrollTop = picker.scrollTop;
          this.constrainScroll("month");
          this.updateMonthFade();
          this.updateSelectedFromScroll("month");
        }
      });
    },

    handleYearTouchEnd() {
      if (this.yearScrollTimeout) {
        clearTimeout(this.yearScrollTimeout);
      }
      this.yearScrollTimeout = setTimeout(() => {
        this.snapToCenter("year");
      }, 150); // 더 부드러운 스냅을 위해 시간 증가
    },

    handleMonthTouchEnd() {
      if (this.monthScrollTimeout) {
        clearTimeout(this.monthScrollTimeout);
      }
      this.monthScrollTimeout = setTimeout(() => {
        this.snapToCenter("month");
      }, 150); // 더 부드러운 스냅을 위해 시간 증가
    },

    updateSelectedFromScroll(type) {
      const picker =
        type === "year" ? this.$refs.yearPicker : this.$refs.monthPicker;
      const items =
        type === "year" ? this.availableYears : this.availableMonths;

      if (!picker || items.length === 0) return;

      const itemHeight = 50;
      const scrollTop = picker.scrollTop;
      const containerHeight = picker.offsetHeight;
      const centerOffset = (containerHeight - itemHeight) / 2;

      // 현재 스크롤 위치에서 중앙에 가장 가까운 아이템 인덱스 계산
      const rawIndex = (scrollTop + centerOffset) / itemHeight;
      let currentIndex = Math.round(rawIndex);

      // 인덱스를 유효한 범위로 제한
      currentIndex = Math.max(0, Math.min(currentIndex, items.length - 1));

      // 스크롤이 범위를 벗어났을 때 강제로 첫 번째나 마지막 값 선택
      if (scrollTop < -centerOffset) {
        currentIndex = 0;
      } else if (scrollTop > (items.length - 1) * itemHeight - centerOffset) {
        currentIndex = items.length - 1;
      }

      if (type === "year") {
        this.selectedYear = items[currentIndex];
      } else {
        this.selectedMonth = items[currentIndex];
      }
    },

    snapToCenter(type) {
      const picker =
        type === "year" ? this.$refs.yearPicker : this.$refs.monthPicker;
      const items =
        type === "year" ? this.availableYears : this.availableMonths;
      const selectedItem =
        type === "year" ? this.selectedYear : this.selectedMonth;

      if (!picker) return;

      if (type === "year") {
        this.isYearSnapping = true;
      } else {
        this.isMonthSnapping = true;
      }

      const itemHeight = 50;
      const containerHeight = picker.offsetHeight;
      const centerOffset = (containerHeight - itemHeight) / 2;

      const selectedIndex = items.indexOf(selectedItem);
      const targetScrollTop = selectedIndex * itemHeight - centerOffset;

      // CSS scroll-behavior를 일시적으로 smooth로 설정
      picker.style.scrollBehavior = "smooth";
      picker.scrollTo({ top: targetScrollTop, behavior: "smooth" });

      setTimeout(() => {
        picker.style.scrollBehavior = "";
        if (type === "year") {
          this.isYearSnapping = false;
        } else {
          this.isMonthSnapping = false;
        }
      }, 400); // 더 부드러운 애니메이션을 위해 시간 증가
    },

    updateYearFade() {
      const picker = this.$refs.yearPicker;
      if (!picker) return;

      const containerHeight = 150; // 고정값 사용으로 DOM 읽기 최소화
      const scrollTop = picker.scrollTop;
      const centerY = containerHeight / 2;
      const styles = {};

      this.availableYears.forEach((_, index) => {
        const itemTop = index * 50 - scrollTop;
        const itemCenter = itemTop + 25;
        const distance = Math.abs(itemCenter - centerY);

        let opacity = 1;
        let scale = 1;
        let fontSize = 32;

        if (distance <= 25) {
          opacity = 1;
          scale = 1;
          fontSize = 32;
        } else if (distance <= 75) {
          const ratio = (distance - 25) / 50;
          opacity = Math.max(0.6, 1 - ratio * 0.4);
          scale = Math.max(0.7, 1 - ratio * 0.3);
          fontSize = Math.max(20, 32 - ratio * 12);
        } else {
          opacity = Math.max(0.3, 1 - ((distance - 75) / 75) * 0.3);
          scale = Math.max(0.6, 1 - ((distance - 75) / 75) * 0.1);
          fontSize = 18;
        }

        styles[index] = {
          opacity: opacity,
          transform: `scale(${scale})`,
          fontSize: `${fontSize}px`,
          transition: "none", // 스크롤 중에는 트랜지션 제거
        };
      });

      this.yearFadeStyles = styles;
    },

    updateMonthFade() {
      const picker = this.$refs.monthPicker;
      if (!picker) return;

      const containerHeight = 150; // 고정값 사용으로 DOM 읽기 최소화
      const scrollTop = picker.scrollTop;
      const centerY = containerHeight / 2;
      const styles = {};

      this.availableMonths.forEach((_, index) => {
        const itemTop = index * 50 - scrollTop;
        const itemCenter = itemTop + 25;
        const distance = Math.abs(itemCenter - centerY);

        let opacity = 1;
        let scale = 1;
        let fontSize = 32;

        if (distance <= 25) {
          opacity = 1;
          scale = 1;
          fontSize = 32;
        } else if (distance <= 75) {
          const ratio = (distance - 25) / 50;
          opacity = Math.max(0.6, 1 - ratio * 0.4);
          scale = Math.max(0.7, 1 - ratio * 0.3);
          fontSize = Math.max(20, 32 - ratio * 12);
        } else {
          opacity = Math.max(0.3, 1 - ((distance - 75) / 75) * 0.3);
          scale = Math.max(0.6, 1 - ((distance - 75) / 75) * 0.1);
          fontSize = 18;
        }

        styles[index] = {
          opacity: opacity,
          transform: `scale(${scale})`,
          fontSize: `${fontSize}px`,
          transition: "none", // 스크롤 중에는 트랜지션 제거
        };
      });

      this.monthFadeStyles = styles;
    },

    scrollToSelected() {
      this.$nextTick(() => {
        const yearPicker = this.$refs.yearPicker;
        if (yearPicker) {
          const selectedYearIndex = this.availableYears.indexOf(
            this.selectedYear
          );
          if (selectedYearIndex >= 0) {
            const itemHeight = 50;
            const containerHeight = yearPicker.offsetHeight;
            const centerOffset = (containerHeight - itemHeight) / 2;
            const scrollTop = selectedYearIndex * itemHeight - centerOffset;
            yearPicker.scrollTo({ top: scrollTop, behavior: "smooth" });
          }
        }

        const monthPicker = this.$refs.monthPicker;
        if (monthPicker) {
          const selectedMonthIndex = this.availableMonths.indexOf(
            this.selectedMonth
          );
          if (selectedMonthIndex >= 0) {
            const itemHeight = 50;
            const containerHeight = monthPicker.offsetHeight;
            const centerOffset = (containerHeight - itemHeight) / 2;
            const scrollTop = selectedMonthIndex * itemHeight - centerOffset;
            monthPicker.scrollTo({ top: scrollTop, behavior: "smooth" });
          }
        }
      });
    },

    confirmSelection() {
      this.$emit("date-selected", {
        year: this.selectedYear,
        month: this.selectedMonth,
      });

      this.$store.commit("SET_SELECTED_DATE", {
        year: this.selectedYear,
        month: this.selectedMonth,
      });

      this.closeModal();
    },
  },
  mounted() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    this.selectedYear = currentYear;
    if (currentMonth > 1) {
      this.selectedMonth = currentMonth - 1;
    } else {
      this.selectedYear = currentYear - 1;
      this.selectedMonth = 12;
    }
  },
  beforeDestroy() {
    if (this.isModalOpen) {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
      window.scrollTo(0, this.scrollY);
    }

    // 모든 타이머와 애니메이션 프레임 정리
    if (this.yearScrollTimeout) {
      clearTimeout(this.yearScrollTimeout);
    }
    if (this.monthScrollTimeout) {
      clearTimeout(this.monthScrollTimeout);
    }
    if (this.yearAnimationFrame) {
      cancelAnimationFrame(this.yearAnimationFrame);
    }
    if (this.monthAnimationFrame) {
      cancelAnimationFrame(this.monthAnimationFrame);
    }
  },
};
</script>

<style lang="scss" scoped>
.select-date-modal {
  $modal-background: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.select-date-modal::backdrop {
  background: transparent;
}

.slide-up-enter-active {
  transition: transform 0.3s ease-out;
}

.slide-up-leave-active {
  transition: transform 0.3s ease-in;
}

.slide-up-enter-from {
  transform: translateY(100%);
}

.slide-up-enter-to {
  transform: translateY(0);
}

.slide-up-leave-from {
  transform: translateY(0);
}

.slide-up-leave-to {
  transform: translateY(100%);
}

.modal-item {
  width: 100%;
  background: white;
  padding: 30px;
  border-radius: 20px 20px 0 0;

  > p {
    text-align: start;
    color: #000000;
    font-weight: bold;
    font-size: 20px;
    letter-spacing: -3%;
    line-height: 25px;
    margin-bottom: 30px;
  }
}

.picker-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 15px;
}

.picker-column {
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.picker-wrapper {
  width: 100px;
  height: 150px;
  overflow-y: auto;
  scroll-behavior: auto;
  padding: 50px 0;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.picker-item {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: #a7a7a7;
  cursor: pointer;
  will-change: transform, opacity, font-size; /* GPU 가속 활성화 */

  &.active {
    font-size: 32px;
    font-weight: 800;
    color: #000000;
  }
}

.confirm-button {
  width: 100%;
  height: 50px;
  background: #41d8e6;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #41d8e6;
  }

  &:active {
    background: #0097a7;
  }
}
</style>
