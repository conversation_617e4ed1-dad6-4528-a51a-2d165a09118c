/**
 *
 * @param {Array} data
 * @param {Boolean} isIos
 *
 */
export function progressCareCalendarCalculate(data, isIos) {
  const result = [];
  // Get today's date and set its time to 00:00:00 for accurate comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0, len = data.length; i < len; i++) {
    const { date, urine, water, weight, bloodPressure, step, woman } = data[i];

    // Create a Date object from the current data's date string
    const dataDate = new Date(date);
    dataDate.setHours(0, 0, 0, 0); // Normalize time to 00:00:00

    // Check if the current date is before today
    if (dataDate > today) {
      // If the date is in the past, stop the loop.
      // This will break the loop at the first occurrence of a past date.
      break;
    }

    let p = 0;
    if (urine && urine.length !== 0) p++;
    if (water && water.length !== 0) p++;
    if (weight && weight.length !== 0) p++;
    if (bloodPressure && bloodPressure.length !== 0) p++;
    if (!isIos) {
      // 체 수 배 혈 걸
      if (step && step !== null) p++;
    }
    const total = isIos ? 4 : 5;

    const progress = Math.round((p / total) * 100);

    result.push({
      date,
      progress,
    });
  }

  return result;
}
/**
 * @param {string} date
 * @param {Object} responseData - API 응답 데이터 { data: Array, menstruation: Object }
 */
export function detailCareCalendarData(date, responseData) {
  // responseData가 배열인지 객체인지 확인
  const dataArray = Array.isArray(responseData)
    ? responseData
    : responseData.data;
  const menstruation = responseData.menstruation || null;

  if (!dataArray || !Array.isArray(dataArray)) {
    console.warn("Invalid data format:", responseData);
    return {
      data: {
        weight: [],
        water: [],
        urine: [],
        bloodPressure: [],
        step: null,
        woman: null,
      },
      menstruation: menstruation,
    };
  }

  for (const dateData of dataArray) {
    if (date === dateData.date) {
      const { progress, ...detail } = dateData;
      return {
        data: detail,
        menstruation: menstruation,
      };
    }
  }

  // 해당 날짜의 데이터를 찾지 못한 경우 기본값 반환
  return {
    data: {
      weight: [],
      water: [],
      urine: [],
      bloodPressure: [],
      step: null,
      woman: null,
    },
    menstruation: menstruation,
  };
}
